"use client";
import { useState, useEffect, useRef } from "react";
import "./styles/hide.css";
import Image from 'next/image';
import Section from "./components/Section";
import Services from './components/Services';
import Projects from './components/Projects';
import Languages from './components/languages';
import Intro from "./components/Intro";
import { gsap } from "gsap";


const Home: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true); 
  const preloaderRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    document.body.style.backgroundColor = 'black';

    const timer = setTimeout(() => {
      setIsLoading(false);

      // Hide the preloader and show the content
      if (preloaderRef.current) {
        preloaderRef.current.style.display = "none";
      }

      if (contentRef.current) {
        contentRef.current.style.opacity = "1";
        contentRef.current.style.display = "block";

        // Trigger GSAP animations for the first section (Intro)
        animateIntroSection();
      }

      // After content is loaded, reset the background color
      document.body.style.backgroundColor = '';
    }, 2000);

    return () => {
      clearTimeout(timer);
      // Reset the background color on cleanup (if unmounted before timeout)
      document.body.style.backgroundColor = '';
    };
  }, []);

  const animateIntroSection = () => {
    // Create a timeline for coordinated animations
    const tl = gsap.timeline();

    // Set initial states for all animated elements
    gsap.set([".nav", ".Creative", ".intro_texts p", ".scroll"], {
      opacity: 0,
      y: 50
    });

    gsap.set(".right", {
      opacity: 0,
      scale: 0.8,
      x: 100
    });

    gsap.set(".logo", {
      opacity: 0,
      scale: 0.5,
      rotation: -180
    });

    gsap.set(".barr", {
      opacity: 0,
      x: 50
    });

    // Animate elements in sequence with overlapping timing
    tl.to(".logo", {
      opacity: 1,
      scale: 1,
      rotation: 0,
      duration: 1,
      ease: "back.out(1.7)"
    })
    .to(".barr", {
      opacity: 1,
      x: 0,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.5")
    .to(".Creative", {
      opacity: 1,
      y: 0,
      duration: 1.2,
      ease: "power3.out"
    }, "-=0.3")
    .to(".intro_texts p", {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power2.out"
    }, "-=0.6")
    .to(".scroll", {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power2.out"
    }, "-=0.4")
    .to(".right", {
      opacity: 1,
      scale: 1,
      x: 0,
      duration: 1.5,
      ease: "power2.out"
    }, "-=1.2");

    // Add a subtle floating animation to the scroll element
    gsap.to(".scroll", {
      y: -10,
      duration: 2,
      ease: "power1.inOut",
      yoyo: true,
      repeat: -1,
      delay: 2
    });

    // Add a subtle scale animation to the Creative text
    gsap.to(".Creative", {
      scale: 1.02,
      duration: 3,
      ease: "power1.inOut",
      yoyo: true,
      repeat: -1,
      delay: 1.5
    });
  };

  return (
    <div>
      {isLoading && 
       <div id="preloader" ref={preloaderRef}>
          <Image
            src="/Assets/Logo.svg" 
            alt="Loading"
            width={100}
            height={100}
            priority
          />
        </div>}

      <div id="content" ref={contentRef} style={{ opacity: 1, display: 'none'}}>
        
        <Intro/>
        <Section title="What Do We Offer" heading="Our Services" />
        <Services />
        <Section title="Works" heading="Our Projects" />
        <Projects />
        <Section title="Experience" heading="Knowledge" />
        <Languages />
        <br /><br /><br />
      </div>
    </div>
  );
};

export default Home;
