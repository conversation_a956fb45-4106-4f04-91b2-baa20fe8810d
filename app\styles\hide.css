@font-face {
  font-family: Monument;
  src: url(/MonumentExtended/MonumentExtended-FreeForPersonalUse/MonumentExtended-Regular.otf);
  font-display: swap;
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: Monument;
}

body {
  overflow-x: hidden;
}

#preloader {
  width: 100px;
  height: 100px;
  background-size: contain;
  background-repeat: no-repeat;
  animation: scale 1.7s linear infinite;
  margin: 200px auto;
  opacity: 1;
  transition: opacity 0.5s ease-out;
  transform: translate(-50%, -50%);
  z-index: 9999;
}

/* Content Styles */
#content {
  opacity: 0;
  transition: opacity 0.5s ease-in;
  display: none;
}

/* GSAP Animation Initial States */
.intro .nav,
.intro .Creative,
.intro .intro_texts p,
.intro .scroll,
.intro .right {
  opacity: 0;
}

/* Ensure smooth animations */
.intro * {
  will-change: transform, opacity;
}

/* Preloader Fade Out */
.loaded {
  opacity: 0 !important;
}

/* Content Fade In */
.visible {
  opacity: 1 !important;
  display: block;
}

/* Preloader Animation */
@keyframes scale {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.intro,
.intro_projects {
  width: 100%;
  height: 100vh;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
.intro_projects {
  grid-template-columns: repeat(1, 1fr);
  height: 70vh;
}
.left,
.left_Projects {
  background-color: #131212;
}
.right {
  position: relative;
}
.desktop-image {
  display: block;
}
.mobile-image {
  display: none;
}
.nav {
  display: flex;
  justify-content: space-between;
  padding: 10px 15px 0;
}

.barr,
.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.Creative {
  width: fit-content;
  margin: 120px auto;
  font-size: 150px;
  color: white;
  position: relative;
  font-weight: bold;
  z-index: 1;
}
.ProjectsPro {
  width: fit-content;
  margin: 75px auto;
  font-size: 150px;
  color: white;
  position: relative;
  font-weight: bold;
  z-index: 1;
}
.ProjectsPro::before {
  content: "Our";
  position: absolute;
  left: 0;
  top: -80px;
  font-size: 90px;
  font-weight: normal;
}
.Creative::before {
  content: "The";
  position: absolute;
  left: 0;
  top: -80px;
  font-size: 90px;
  font-weight: normal;
}
.Creative::after {
  content: "Agengy";
  position: absolute;
  right: 0;
  bottom: -80px;
  font-size: 90px;
  font-weight: normal;
}

.intro_texts {
  padding: 20px;
  position: absolute;
  width: 100%;
  height: 100%;
  margin: auto;
  z-index: 1;
}
.intro_texts p {
  font-size: 14px;
  font-family: sans-serif;
  color: rgba(255, 255, 255, 0.388);
  margin: -20px;
  width: 400px;
  padding: 0px 30px;
  text-align: start;
}
.scroll {
  display: none;
}
.section {
  margin: 20px auto 50px;
  width: fit-content;
  text-align: center;
}
.section span {
  font-size: 11px;
  color: #331a54;
}
.Services {
  text-align: center;
  margin: 50px auto;
  color: #331a54;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(1, 1fr);
  width: 90%;
}
.Services div h3 {
  margin: 20px;
}
.service {
  width: 350px;
  height: 350px;
  background-size: cover;
  background-repeat: no-repeat;
  margin: auto;
  background-position: center;
}
.projects {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  width: 95%;
  gap: 20px;
  margin: auto;
}
.box {
  width: 98%;
  height: 350px;
  background-color: #44256883;
  color: white;
  margin: 20px auto;
  border-radius: 30px;
  padding: 10px;
}
a {
  text-decoration: none;
}
.box p {
  padding: 2px;
}
.box_Desctription p {
  font-size: 12px;
  margin-top: 5px;
  color: rgba(0, 0, 0, 0.655);
}
.box_Desctription {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
}
.ProgrammedBy {
  background-image: url(Assets/git.svg);
}
.multiple {
  margin: 0px auto 10px;
  width: 90%;
  height: 250px;
  position: relative;
}
.multiple div {
  border-radius: 20px;
}
#box_1 {
  position: absolute;
  height: 100%;
  width: 80%;
  background-color: #512d77;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
#box_2 {
  position: absolute;
  height: 95%;
  width: 90%;
  background-color: #31313d;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.box_3 {
  background-size: cover;
  background-repeat: no-repeat;
  position: absolute;
  height: 90%;
  width: 100%;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 20px;
  overflow: hidden;
}
#direction {
  position: absolute;
  right: 10px;
  bottom: 10px;
  background-image: url(Assets/OutLink.svg);
  background-color: black;
  background-repeat: no-repeat;
  background-size: contain;
  width: 28px;
  height: 30px;
}

.languages {
  display: grid;
  grid-template-columns: repeat(9, 1fr);
  gap: 40px 20px;
  padding: 5px 100px;
  width: 80%;
  margin: auto;
}
.languages > div {
  background-size: cover;
  background-repeat: no-repeat;
  width: 40px;
  height: 40px;
  margin: auto;
}

.see {
  display: block;
  width: 60%;
  padding: 10px 20px;
  border-radius: 20px;
  color: white;
  background-color: #000;
  margin: 0px auto 20px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 20px;
  text-decoration: none;
  position: relative; /* For positioning */
  overflow: hidden; /* Prevent border animation overflow */
  border: 2px solid transparent; /* Transparent border for effect */
  background-clip: padding-box; /* Ensures the background doesn't cover the border */
  animation: rotateBorder 3s linear infinite, neonGlow 1.5s infinite alternate; /* Multiple animations */
}

@keyframes neonGlow {
  0% {
  }
  50% {
    text-shadow: 0 0 0px #fff, 0 0 0px #fff, 0 0 0px #fff;
  }
  100% {
    text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #fff;
  }
}

.see:hover {
  color: #fff; /* Neon text color on hover */
  transform: scale(1.1);
}

#seemore {
  background-image: url(Assets/Right_Der.svg);
  background-size: contain;
  background-repeat: no-repeat;
  width: 30px;
  height: 30px;
  animation: neonDire 1.5s linear infinite;
}

.fullscreen-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
  z-index: 999;
}

.fullscreen-nav.open {
  transform: translateX(0);
}

.fullscreen-nav ul {
  list-style: none;
  padding: 0;
  text-align: center;
}

.fullscreen-nav li {
  padding: 20px;
  font-size: 24px;
  cursor: pointer;
}

.fullscreen-nav li:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

@media (max-width: 768px) {
  .see {
    display: block;
    width: 60%;
    padding: 10px 20px;
    border-radius: 20px;
    color: white;
    background-color: #000;
    margin: -10px auto 0px;
    text-align: center;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }
  .languages {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 40px 20px;
    padding: 5px 50px;
    margin: auto;
    width: 100%;
  }
  .languages > div {
    background-size: cover;
    background-repeat: no-repeat;
    width: 40px;
    height: 40px;
    margin: auto;
  }

  .Services {
    width: 90%;
    grid-template-columns: repeat(1, 1fr);
  }
  .service {
    width: 100%;
    height: 350px;
    margin: auto;
  }
  .intro_texts p {
    font-size: 14px;
    font-family: sans-serif;
    color: rgba(255, 255, 255, 0.388);
    width: auto;
    text-align: center;
    margin: auto;
  }
  .scroll {
    display: block;
    margin: 15px auto;
    width: fit-content;
    font-size: 11px;
    color: #fff;
  }
  .logo_scroll {
    background-repeat: no-repeat;
    background-size: contain;
    width: 30px;
    height: 50px;
    position: absolute;
    left: 50%;
    bottom: -70px;
    transform: translateX(-50%);
    animation: scrol 2s linear infinite;
    transition: opacity 0.5s ease-out;
  }
  @keyframes scrol {
    0% {
      bottom: -70px;
      opacity: 1;
    }
    50% {
      bottom: -80px;
      opacity: 0.8;
    }
    100% {
      bottom: -70px;
      opacity: 1;
    }
  }

  .scrollBox {
    position: relative;
  }
  .intro,
  .intro_projects {
    grid-template-columns: repeat(1, 1fr);
    height: 80vh;
  }
  .intro_projects {
    height: 50vh;
  }
  .left {
    display: none;
  }
  .desktop-image {
    display: none;
  }

  .mobile-image {
    display: block;
  }

  .Creative {
    font-size: 55px;
    font-weight: bolder;
  }
  .Creative::before {
    top: -30px;
    font-size: 30px;
  }
  .ProjectsPro {
    font-size: 50px;
    font-weight: bolder;
  }
  .ProjectsPro::before {
    top: -30px;
    font-size: 30px;
  }
  .Creative::after {
    bottom: -30px;
    font-size: 30px;
  }
  .projects {
    grid-template-columns: repeat(1, 1fr);
    gap: 0px;
  }
}
