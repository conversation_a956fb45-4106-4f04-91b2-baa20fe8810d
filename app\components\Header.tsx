import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";

export default function Header() {
  const [isNavOpen, setIsNavOpen] = useState(false);
  const toggleNav = () => {
    setIsNavOpen(!isNavOpen);
  };

  useEffect(() => {
    if (isNavOpen) {
      document.body.style.overflowY = "hidden";
      document.documentElement.style.overflowY = "hidden";
    } else {
      document.body.style.overflowY = "auto";
      document.documentElement.style.overflowY = "auto";
    }

    return () => {
      document.body.style.overflowY = "auto";
      document.documentElement.style.overflowY = "auto";
    };
  }, [isNavOpen]);

  return (
    <div>
      <div className="nav">
        <Link href="/">
          <div className="logo">
            <Image src="/Assets/Logo.svg" alt="Logo" width={50} height={50} />
          </div>
        </Link>
        <div className="barr" onClick={toggleNav}>
          <Image
            src={isNavOpen ? "/Assets/close.svg" : "/Assets/barr.svg"}
            alt={isNavOpen ? "Close Icon" : "Menu Icon"}
            width={30}
            height={30}
          />
        </div>
      </div>
      <div className={`fullscreen-nav ${isNavOpen ? "open" : ""}`}>
        <ul>
          <li>
            <Link href="/">Home</Link>
          </li>
          <li>
            <Link href="/Services">Services</Link>
          </li>
          <li>
            <Link href="/Projects">Projects</Link>
          </li>
          <li>
            <Link href="/Knowledge">Knowledge</Link>
          </li>
          <li>
            <Link href="/Contact">Contact</Link>
          </li>
        </ul>
      </div>
    </div>
  );
}
